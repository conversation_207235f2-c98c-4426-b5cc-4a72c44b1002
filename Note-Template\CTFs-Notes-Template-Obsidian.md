---
type: {notes/writeup}
tags: [{PLATFORM NAME/CTF NAME}, {box/challenge}, {linux/win}, {easy/medium/hard}, {services}, {cve-number}, {tooling}, {techniques}]
reference: {URL}
category: {blackbox/web/rev-engineering/rooting/coding/hardware/blockchain/misc/steg/etc}
title: "{BOX NAME}"
difficulty: {easy, medium, hard}
user-voted: {easy, medium, hard}
date: {YYYY-MM-DD}
author: {AUTHOR NAME}
contact: {AUTHOR CONTACT}
---

# {BOX NAME}

Target IP = {BOX IP}
Tun0 IP = {VPN ATTACKER IP}

---

## Information Gathering

### Open Ports & Running Services

```sh
{COMMAND}


```

**OS:** {Linux/Win}

| Port #   |      Service      |  
| ------   | ----------------- |  
| #        | {NAME  + VERSION} |  
| #        | {NAME  + VERSION} |  
| #        | {NAME  + VERSION} |  

---

## Exploitation

### {PORT } - {SERVICE NAME}

### {Step#}

Tooling: {TOOL NAME}
Description: {GOALS}

```sh
{COMMAND}


```

{SCREENSHOTS}

---

### {Step#}

Tooling: {TOOL NAME}
Description: {GOALS}

```sh
{COMMAND}

```

{SCREENSHOTS}

---

> [!USER FLAG]
> {USER FLAG}

{SCREENSHOT}

---

## Privilege Escalation

### {Step#}

Tooling: {TOOL NAME}
Description: {GOALS}

```sh
{COMMAND}

```

{SCREENSHOTS}

---

### {Step#}

Tooling: {TOOL NAME}
Description: {GOALS}

```sh
{COMMAND}

```

{SCREENSHOTS}

---

> [!ROOT FLAG]  
> {ROOT FLAG}  

{SCREENSHOT}

---

## SUCCESS

---

### Conclusion

{SCREENSHOT FINAL SCREEN}

---

## TODO

[ ] Share on Twitter  
[ ] Revise notes + Links to Notebook  
[ ] Export to HTML  
[ ] Publish on Medium  
[ ] Convert to PDF  
[ ] Push on GitHub  

