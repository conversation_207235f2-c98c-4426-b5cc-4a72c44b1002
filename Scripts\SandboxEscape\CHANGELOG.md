# Changelog

## 0.1.0 - 2025-05-16

### Added
- Initial basic version of the BashSandboxEscape tool (`bash_sandbox_escape.py`).
- Command-line interface with arguments for host, port, regex, and modules.
- Payload loading from modular text files in the `payloads/` directory.
- Payload filtering based on allowed characters regex.
- Basic socket-based connection and payload sending/receiving.
- Created `README.md` with project description, features, and usage example.
