[{"payload": "$(/???/ls)", "response": "⢳⠀⢸⢳⢸⠀⡇⣎⡇⢸⠽⢠⠇⠁⣰⡿⢡⣾⢞⣴⣯⡔⣫⢴⠟⠀⠀\r\n⠀⠀⠙⢦⣝⢦⡑⢄⠳⣌⠳⡈⢧⠈⡆⠸⣼⠸⡀⡇⡏⠁⡏⢀⠎⠀⡰⠉⡱⠋⡡⣫⣞⣽⣋⣴⠟⠀⠀⠀\r\n⠀⠀⠀⠀⠉⠻⢿⣮⡳⣌⢦⡱⡌⣆⢸⠀⡟⡆⡇⡇⡇⢸⠁⡜⠀⡼⠁⡜⠁⣪⠞⣿⡵⠟⠉⠀⠀⠀⠀⠀\r\n⠀⠀⠀⠀⠀⠀⠀⠙⡾⣾⣷⣵⡜⡜⡄⠇⢣⡇⡇⢳⠁⡎⠀⠀⡼⢁⠞⠠⢾⡵⠟⠉⠀⠀⠀⠀⠀⠀⠀⠀\r\n⠀⠀⠀⠀⠀⠀⠀⠀⣷⠈⠛⢿⣾⣾⣾⠘⠈⠁⠇⠀⠀⠁⠀⠾⠁⢀⣤⠞⢫⡗⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀\r\n⠀⠀⠀⠀⠀⠀⠀⠀⠘⢧⣄⠀⠙⠿⣟⠀⠀⠀⠀⠀⠀⠀⠀⢀⡴⠛⣡⡴⠋⠁⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀\r\n⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠈⠙⠒⠦⠬⠷⢄⡀⠀⠀⠀⣠⠴⠟⠚⠉⠁⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀\r\n⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠉⠛⠒⠋⠁⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀\r\n\r\n\u001b[33m\r\n[*] Allowed characters: ^[0-9${}/?\"[:space:]:&>_=()]+$\r\n\u001b[0m\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/ls /)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/ls /???)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/ls /???/)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/ls /????)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/ls /????/)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/ls /????/????)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/cat /???/????)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/cat /???/????/????)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/cat /????/????)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/cat /????/????/????)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/cat /????/????/????/????)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/id)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/env)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/pwd)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/ps)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/ls /???/???)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/ls /???/????)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/ls /???/???/)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/ls /???/????/)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/find / -name ????)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/find / -name ????.???)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/grep ???? /)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/grep ???? /????/????)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/sh)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/bash)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/cp /????/????/???? /???/????)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/echo $PATH)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/echo $PWD)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/echo $USER)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/ls /?)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/ls /??)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/ls /???)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/cat /????)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/cat /???/????)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/cat /????/????)", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}]