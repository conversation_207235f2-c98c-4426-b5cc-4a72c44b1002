[{"payload": "/???/?? /??? /???/????", "response": "⢳⠀⢸⢳⢸⠀⡇⣎⡇⢸⠽⢠⠇⠁⣰⡿⢡⣾⢞⣴⣯⡔⣫⢴⠟⠀⠀\r\n⠀⠀⠙⢦⣝⢦⡑⢄⠳⣌⠳⡈⢧⠈⡆⠸⣼⠸⡀⡇⡏⠁⡏⢀⠎⠀⡰⠉⡱⠋⡡⣫⣞⣽⣋⣴⠟⠀⠀⠀\r\n⠀⠀⠀⠀⠉⠻⢿⣮⡳⣌⢦⡱⡌⣆⢸⠀⡟⡆⡇⡇⡇⢸⠁⡜⠀⡼⠁⡜⠁⣪⠞⣿⡵⠟⠉⠀⠀⠀⠀⠀\r\n⠀⠀⠀⠀⠀⠀⠀⠙⡾⣾⣷⣵⡜⡜⡄⠇⢣⡇⡇⢳⠁⡎⠀⠀⡼⢁⠞⠠⢾⡵⠟⠉⠀⠀⠀⠀⠀⠀⠀⠀\r\n⠀⠀⠀⠀⠀⠀⠀⠀⣷⠈⠛⢿⣾⣾⣾⠘⠈⠁⠇⠀⠀⠁⠀⠾⠁⢀⣤⠞⢫⡗⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀\r\n⠀⠀⠀⠀⠀⠀⠀⠀⠘⢧⣄⠀⠙⠿⣟⠀⠀⠀⠀⠀⠀⠀⠀⢀⡴⠛⣡⡴⠋⠁⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀\r\n⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠈⠙⠒⠦⠬⠷⢄⡀⠀⠀⠀⣠⠴⠟⠚⠉⠁⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀\r\n⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠉⠛⠒⠋⠁⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀\r\n\r\n\u001b[33m\r\n[*] Allowed characters: ^[0-9${}/?\"[:space:]:&>_=()]+$\r\n\u001b[0m\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m/bin/cp: cannot create regular file '/var/mail/dd': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/df': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/du': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/id': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/ln': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/ls': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/mv': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/nl': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/od': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/pr': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/ps': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/rm': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/sg': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/sh': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/su': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/tr': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/wc': Permission denied\r\n/bin/cp: -r not specified; omitting directory '/dev/fd'\r\n/bin/cp: -r not specified; omitting directory '/sys/fs'\r\n/bin/cp: -r not specified; omitting directory '/bin'\r\n/bin/cp: -r not specified; omitting directory '/dev'\r\n/bin/cp: -r not specified; omitting directory '/etc'\r\n/bin/cp: -r not specified; omitting directory '/lib'\r\n/bin/cp: -r not specified; omitting directory '/mnt'\r\n/bin/cp: -r not specified; omitting directory '/opt'\r\n/bin/cp: -r not specified; omitting directory '/run'\r\n/bin/cp: -r not specified; omitting directory '/srv'\r\n/bin/cp: -r not specified; omitting directory '/sys'\r\n/bin/cp: -r not specified; omitting directory '/tmp'\r\n/bin/cp: -r not specified; omitting directory '/usr'\r\n/bin/cp: -r not specified; omitting directory '/var'\r\n/bin/cp: cannot create regular file '/var/mail/arch': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/bash': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/chfn': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/chrt': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/chsh': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/comm': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/dash': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/date': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/diff': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/dpkg': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/echo': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/expr': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/find': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/fold': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/free': Permission deni"}, {"payload": "/???/?? /??? /???/?", "response": "ed\r\n/bin/cp: cannot create regular file '/var/mail/gpgv': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/grep': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/gzip': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/head': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/i386': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/ipcs': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/join': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/kill': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/last': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/link': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/lsns': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/mawk': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/mesg': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/more': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/nawk': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/nice': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/perl': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/pldd': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/pmap': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/pwdx': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/shuf': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/sort': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/stat': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/stty': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/sync': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/tabs': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/tail': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/test': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/tput': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/true': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/tset': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/uniq': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/vdir': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/wall': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/zcat': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/zcmp': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/znew': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/core': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/full': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/null': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/ptmx': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/zero': Permission denied\r\n/bin/cp: -r not specified; omitting directory '/etc/dpkg'\r\n/bin/cp: -r not specified; omitting directory '/etc/skel'\r\n/bin/cp: -r not specified; omitting directory '/lib/dpkg'\r\n/bin/cp: -r not specified; omitting directory '/lib/init'\r\n/bin/cp: -r not specified; omitting directory '/lib/mime'\r\n/bin/cp: -r not specified; omitting directory '/lib/udev'\r\n/bin/cp: -r not specified; omitting directory '/run/lock'\r\n/bin/cp: -r not specified; omitting directory '/usr/sbin'\r\n/bin/cp: -r not specified; omitting directory '/var/lock'\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m/bin/cp: target '/bin/w' is not a directory\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "/???/?? /??? /???", "response": "/bin/cp: cannot create regular file '/var/dd': Permission denied\r\n/bin/cp: cannot create regular file '/var/df': Permission denied\r\n/bin/cp: cannot create regular file '/var/du': Permission denied\r\n/bin/cp: cannot create regular file '/var/id': Permission denied\r\n/bin/cp: cannot create regular file '/var/ln': Permission denied\r\n/bin/cp: cannot create regular file '/var/ls': Permission denied\r\n/bin/cp: cannot create regular file '/var/mv': Permission denied\r\n/bin/cp: cannot create regular file '/var/nl': Permission denied\r\n/bin/cp: cannot create regular file '/var/od': Permission denied\r\n/bin/cp: cannot create regular file '/var/pr': Permission denied\r\n/bin/cp: cannot create regular file '/var/ps': Permission denied\r\n/bin/cp: cannot create regular file '/var/rm': Permission denied\r\n/bin/cp: cannot create regular file '/var/sg': Permission denied\r\n/bin/cp: cannot create regular file '/var/sh': Permission denied\r\n/bin/cp: cannot create regular file '/var/su': Permission denied\r\n/bin/cp: cannot create regular file '/var/tr': Permission denied\r\n/bin/cp: cannot create regular file '/var/wc': Permission denied\r\n/bin/cp: -r not specified; omitting directory '/dev/fd'\r\n/bin/cp: -r not specified; omitting directory '/sys/fs'\r\n/bin/cp: -r not specified; omitting directory '/bin'\r\n/bin/cp: -r not specified; omitting directory '/dev'\r\n/bin/cp: -r not specified; omitting directory '/etc'\r\n/bin/cp: -r not specified; omitting directory '/lib'\r\n/bin/cp: -r not specified; omitting directory '/mnt'\r\n/bin/cp: -r not specified; omitting directory '/opt'\r\n/bin/cp: -r not specified; omitting directory '/run'\r\n/bin/cp: -r not specified; omitting directory '/srv'\r\n/bin/cp: -r not specified; omitting directory '/sys'\r\n/bin/cp: -r not specified; omitting directory '/tmp'\r\n/bin/cp: -r not specified; omitting directory '/usr'\r\n/bin/cp: -r not specified; omitting directory '/var'\r\n/bin/cp: -r not specified; omitting directory '/bin'\r\n/bin/cp: -r not specified; omitting directory '/dev'\r\n/bin/cp: -r not specified; omitting directory '/etc'\r\n/bin/cp: -r not specified; omitting directory '/lib'\r\n/bin/cp: -r not specified; omitting directory '/mnt'\r\n/bin/cp: -r not specified; omitting directory '/opt'\r\n/bin/cp: -r not specified; omitting directory '/run'\r\n/bin/cp: -r not specified; omitting directory '/srv'\r\n/bin/cp: -r not specified; omitting directory '/sys'\r\n/bin/cp: -r not specified; omitting directory '/tmp'\r\n/bin/cp: -r not specified; omitting directory '/usr'\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "/???/?? /???/????? /???/????", "response": "/bin/cp: cannot create regular file '/var/mail/dd': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/df': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/du': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/id': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/ln': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/ls': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/mv': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/nl': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/od': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/pr': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/ps': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/rm': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/sg': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/sh': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/su': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/tr': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/wc': Permission denied\r\n/bin/cp: -r not specified; omitting directory '/dev/fd'\r\n/bin/cp: -r not specified; omitting directory '/sys/fs'\r\n/bin/cp: cannot create regular file '/var/mail/b2sum': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/chage': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/chcon': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/chgrp': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/chmod': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/choom': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/chown': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/cksum': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/clear': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/diff3': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/dmesg': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/egrep': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/false': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/fgrep': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/filan': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/flock': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/gzexe': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/iconv': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/ipcmk': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/ipcrm': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/lastb': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/login': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/lsblk': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/lscpu': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/lsipc': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/lsmem': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/mkdir': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/mknod': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/mount': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/namei': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/nohup': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/nproc': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/pager': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/partx': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/paste': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/pgrep': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/pidof': Permission denied\r\n/bin/cp: cannot create regular file '"}, {"payload": "/???/?? /????/????? /???/????", "response": "/var/mail/pinky': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/pkill': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/rbash': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/reset': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/rgrep': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/rmdir': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/sdiff': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/shred': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/skill': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/sleep': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/snice': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/socat': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/split': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/tload': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/touch': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/tsort': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/uname': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/users': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/watch': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/wdctl': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/which': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/xargs': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/zdiff': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/zdump': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/zgrep': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/zless': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/zmore': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/stdin': Permission denied\r\n/bin/cp: -r not specified; omitting directory '/etc/cloud'\r\n/bin/cp: cannot create regular file '/var/mail/fstab': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/group': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/hosts': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/issue': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/legal': Permission denied\r\n/bin/cp: -r not specified; omitting directory '/etc/pam.d'\r\n/bin/cp: -r not specified; omitting directory '/etc/rc0.d'\r\n/bin/cp: -r not specified; omitting directory '/etc/rc1.d'\r\n/bin/cp: -r not specified; omitting directory '/etc/rc2.d'\r\n/bin/cp: -r not specified; omitting directory '/etc/rc3.d'\r\n/bin/cp: -r not specified; omitting directory '/etc/rc4.d'\r\n/bin/cp: -r not specified; omitting directory '/etc/rc5.d'\r\n/bin/cp: -r not specified; omitting directory '/etc/rc6.d'\r\n/bin/cp: -r not specified; omitting directory '/etc/rcS.d'\r\n/bin/cp: -r not specified; omitting directory '/run/mount'\r\n/bin/cp: -r not specified; omitting directory '/sys/block'\r\n/bin/cp: -r not specified; omitting directory '/sys/class'\r\n/bin/cp: -r not specified; omitting directory '/sys/power'\r\n/bin/cp: -r not specified; omitting directory '/usr/games'\r\n/bin/cp: -r not specified; omitting directory '/usr/lib32'\r\n/bin/cp: -r not specified; omitting directory '/usr/lib64'\r\n/bin/cp: -r not specified; omitting directory '/usr/local'\r\n/bin/cp: -r not specified; omitting directory '/usr/share'\r\n/bin/cp: -r not specified; omitting directory '/var/cache'\r\n/bin/cp: -r not specified; omitting directory '/var/local'\r\n/bin/cp: -r not specified; omitting directory '/var/spool'\r\n/bin/cp: cannot create regular file '/var/mail/arch': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/bash': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/chfn': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/chrt': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/chsh': Permission denied\r\n/bin/c"}, {"payload": "/???/?? /????/???? /???/????", "response": "p: cannot create regular file '/var/mail/comm': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/dash': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/date': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/diff': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/dpkg': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/echo': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/expr': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/find': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/fold': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/free': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/gpgv': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/grep': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/gzip': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/head': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/i386': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/ipcs': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/join': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/kill': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/last': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/link': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/lsns': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/mawk': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/mesg': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/more': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/nawk': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/nice': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/perl': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/pldd': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/pmap': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/pwdx': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/shuf': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/sort': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/stat': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/stty': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/sync': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/tabs': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/tail': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/test': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/tput': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/true': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/tset': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/uniq': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/vdir': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/wall': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/zcat': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/zcmp': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/znew': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/core': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/full': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/null': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/ptmx': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/zero': Permission denied\r\n/bin/cp: -r not specified; omitting directory '/etc/dpkg'\r\n/bin/cp: -r not specified; omitting directory '/etc/skel'\r\n/bin/cp: -r not specified; omitting directory '/lib/dpkg'\r\n/bin/cp: -r not specified; omitting directory '/lib/init'\r\n/bin/cp: -r not specified; omitting directory '/lib/mime'\r\n/bin/cp: -r"}, {"payload": "/???/?? /???? /???/????", "response": " not specified; omitting directory '/lib/udev'\r\n/bin/cp: -r not specified; omitting directory '/run/lock'\r\n/bin/cp: -r not specified; omitting directory '/usr/sbin'\r\n/bin/cp: -r not specified; omitting directory '/var/lock'\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m/bin/cp: cannot create regular file '/var/mail/dd': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/df': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/du': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/id': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/ln': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/ls': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/mv': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/nl': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/od': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/pr': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/ps': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/rm': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/sg': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/sh': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/su': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/tr': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/wc': Permission denied\r\n/bin/cp: -r not specified; omitting directory '/dev/fd'\r\n/bin/cp: -r not specified; omitting directory '/sys/fs'\r\n/bin/cp: -r not specified; omitting directory '/proc/77112'\r\n/bin/cp: -r not specified; omitting directory '/proc/77114'\r\n/bin/cp: cannot stat '/proc/84991': No such file or directory\r\n/bin/cp: cannot create regular file '/var/mail/iomem': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/kcore': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/locks': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/swaps': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/blkid': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/chcpu': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/chmem': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/getty': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/grpck': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/arch': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/bash': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/chfn': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/chrt': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/chsh': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/comm': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/dash': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/date': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/diff': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/dpkg': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/echo': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/expr': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/find': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/fold': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/free': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/gpgv': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/grep': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/gzip': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/head': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/i386': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/ipcs': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/join': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/kill': Permission "}, {"payload": "/???/?? /???? /???", "response": "denied\r\n/bin/cp: cannot create regular file '/var/mail/last': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/link': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/lsns': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/mawk': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/mesg': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/more': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/nawk': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/nice': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/perl': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/pldd': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/pmap': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/pwdx': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/shuf': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/sort': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/stat': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/stty': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/sync': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/tabs': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/tail': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/test': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/tput': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/true': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/tset': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/uniq': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/vdir': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/wall': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/zcat': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/zcmp': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/znew': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/core': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/full': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/null': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/ptmx': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/zero': Permission denied\r\n/bin/cp: -r not specified; omitting directory '/etc/dpkg'\r\n/bin/cp: -r not specified; omitting directory '/etc/skel'\r\n/bin/cp: -r not specified; omitting directory '/lib/dpkg'\r\n/bin/cp: -r not specified; omitting directory '/lib/init'\r\n/bin/cp: -r not specified; omitting directory '/lib/mime'\r\n/bin/cp: -r not specified; omitting directory '/lib/udev'\r\n/bin/cp: -r not specified; omitting directory '/run/lock'\r\n/bin/cp: -r not specified; omitting directory '/usr/sbin'\r\n/bin/cp: -r not specified; omitting directory '/var/lock'\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m/bin/cp: cannot create regular file '/var/mail/dd': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/df': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/du': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/id': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/ln': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/ls': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/mv': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/nl': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/od': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/pr': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/ps': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/rm': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/sg': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/sh': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/su': P"}, {"payload": "/???/?? /????/???? /???", "response": "ermission denied\r\n/bin/cp: cannot create regular file '/var/mail/tr': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/wc': Permission denied\r\n/bin/cp: -r not specified; omitting directory '/dev/fd'\r\n/bin/cp: -r not specified; omitting directory '/sys/fs'\r\n/bin/cp: -r not specified; omitting directory '/proc/acpi'\r\n/bin/cp: cannot create regular file '/var/mail/keys': Permission denied\r\n/bin/cp: cannot open '/proc/kmsg' for reading: Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/misc': Permission denied\r\n/bin/cp: cannot open '/proc/mtrr' for reading: Operation not permitted\r\n/bin/cp: -r not specified; omitting directory '/proc/self'\r\n/bin/cp: cannot create regular file '/var/mail/stat': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/cpgr': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/cppw': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/fsck': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/mkfs': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/pwck': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/vigr': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/vipw': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/arch': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/bash': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/chfn': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/chrt': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/chsh': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/comm': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/dash': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/date': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/diff': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/dpkg': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/echo': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/expr': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/find': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/fold': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/free': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/gpgv': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/grep': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/gzip': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/head': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/i386': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/ipcs': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/join': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/kill': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/last': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/link': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/lsns': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/mawk': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/mesg': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/more': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/nawk': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/nice': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/perl': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/pldd': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/pmap': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/pwdx': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/shuf': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/sort': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/stat': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/stty': Permis"}, {"payload": "/???/? /??? = /???", "response": "sion denied\r\n/bin/cp: cannot create regular file '/var/mail/sync': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/tabs': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/tail': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/test': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/tput': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/true': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/tset': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/uniq': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/vdir': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/wall': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/zcat': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/zcmp': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/znew': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/core': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/full': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/null': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/ptmx': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/zero': Permission denied\r\n/bin/cp: -r not specified; omitting directory '/etc/dpkg'\r\n/bin/cp: -r not specified; omitting directory '/etc/skel'\r\n/bin/cp: -r not specified; omitting directory '/lib/dpkg'\r\n/bin/cp: -r not specified; omitting directory '/lib/init'\r\n/bin/cp: -r not specified; omitting directory '/lib/mime'\r\n/bin/cp: -r not specified; omitting directory '/lib/udev'\r\n/bin/cp: -r not specified; omitting directory '/run/lock'\r\n/bin/cp: -r not specified; omitting directory '/usr/sbin'\r\n/bin/cp: -r not specified; omitting directory '/var/lock'\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m/bin/cp: cannot create regular file '/var/mail/dd': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/df': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/du': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/id': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/ln': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/ls': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/mv': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/nl': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/od': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/pr': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/ps': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/rm': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/sg': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/sh': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/su': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/tr': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/wc': Permission denied\r\n/bin/cp: -r not specified; omitting directory '/dev/fd'\r\n/bin/cp: -r not specified; omitting directory '/sys/fs'\r\n/bin/cp: -r not specified; omitting directory '/boot'\r\n/bin/cp: -r not specified; omitting directory '/home'\r\n/bin/cp: -r not specified; omitting directory '/proc'\r\n/bin/cp: -r not specified; omitting directory '/root'\r\n/bin/cp: -r not specified; omitting directory '/sbin'\r\n/bin/cp: cannot create regular file '/var/mail/arch': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/bash': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/chfn': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/chrt': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/chsh': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/comm': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/dash': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/date': Permission denied\r\n/bin/cp: cannot create regular"}, {"payload": "/???/? /??? = /???/", "response": " file '/var/mail/diff': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/dpkg': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/echo': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/expr': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/find': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/fold': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/free': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/gpgv': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/grep': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/gzip': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/head': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/i386': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/ipcs': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/join': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/kill': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/last': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/link': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/lsns': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/mawk': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/mesg': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/more': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/nawk': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/nice': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/perl': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/pldd': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/pmap': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/pwdx': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/shuf': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/sort': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/stat': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/stty': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/sync': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/tabs': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/tail': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/test': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/tput': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/true': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/tset': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/uniq': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/vdir': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/wall': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/zcat': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/zcmp': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/znew': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/core': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/full': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/null': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/ptmx': Permission denied\r\n/bin/cp: cannot create regular file '/var/mail/zero': Permission denied\r\n/bin/cp: -r not specified; omitting directory '/etc/dpkg'\r\n/bin/cp: -r not specified; omitting directory '/etc/skel'\r\n/bin/cp: -r not specified; omitting directory '/lib/dpkg'\r\n/bin/cp: -r not specified; omitting directory '/lib/init'\r\n/bin/cp: -r not specified; omitting directory '/lib/mime'\r\n/bin/cp: -r not specified; omitting directory '/lib/udev'\r\n/bin/cp: -r not specified; omitting directory '/run/lock'\r\n/bin/cp: -r not specified; omitting directory '/usr/sbin'\r\n/bin/cp: -r not specified; omitting directory '/var/lock'\r\n\u001b[32mBroken@Shell\u001b"}, {"payload": "/???/? /??? = /????", "response": "[33m$ \u001b[0m/bin/cp: cannot create regular file '/var/dd': Permission denied\r\n/bin/cp: cannot create regular file '/var/df': Permission denied\r\n/bin/cp: cannot create regular file '/var/du': Permission denied\r\n/bin/cp: cannot create regular file '/var/id': Permission denied\r\n/bin/cp: cannot create regular file '/var/ln': Permission denied\r\n/bin/cp: cannot create regular file '/var/ls': Permission denied\r\n/bin/cp: cannot create regular file '/var/mv': Permission denied\r\n/bin/cp: cannot create regular file '/var/nl': Permission denied\r\n/bin/cp: cannot create regular file '/var/od': Permission denied\r\n/bin/cp: cannot create regular file '/var/pr': Permission denied\r\n/bin/cp: cannot create regular file '/var/ps': Permission denied\r\n/bin/cp: cannot create regular file '/var/rm': Permission denied\r\n/bin/cp: cannot create regular file '/var/sg': Permission denied\r\n/bin/cp: cannot create regular file '/var/sh': Permission denied\r\n/bin/cp: cannot create regular file '/var/su': Permission denied\r\n/bin/cp: cannot create regular file '/var/tr': Permission denied\r\n/bin/cp: cannot create regular file '/var/wc': Permission denied\r\n/bin/cp: -r not specified; omitting directory '/dev/fd'\r\n/bin/cp: -r not specified; omitting directory '/sys/fs'\r\n/bin/cp: -r not specified; omitting directory '/boot'\r\n/bin/cp: -r not specified; omitting directory '/home'\r\n/bin/cp: -r not specified; omitting directory '/proc'\r\n/bin/cp: -r not specified; omitting directory '/root'\r\n/bin/cp: -r not specified; omitting directory '/sbin'\r\n/bin/cp: -r not specified; omitting directory '/bin'\r\n/bin/cp: -r not specified; omitting directory '/dev'\r\n/bin/cp: -r not specified; omitting directory '/etc'\r\n/bin/cp: -r not specified; omitting directory '/lib'\r\n/bin/cp: -r not specified; omitting directory '/mnt'\r\n/bin/cp: -r not specified; omitting directory '/opt'\r\n/bin/cp: -r not specified; omitting directory '/run'\r\n/bin/cp: -r not specified; omitting directory '/srv'\r\n/bin/cp: -r not specified; omitting directory '/sys'\r\n/bin/cp: -r not specified; omitting directory '/tmp'\r\n/bin/cp: -r not specified; omitting directory '/usr'\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m/bin/cp: cannot create regular file '/var/dd': Permission denied\r\n/bin/cp: cannot create regular file '/var/df': Permission denied\r\n/bin/cp: cannot create regular file '/var/du': Permission denied\r\n/bin/cp: cannot create regular file '/var/id': Permission denied\r\n/bin/cp: cannot create regular file '/var/ln': Permission denied\r\n/bin/cp: cannot create regular file '/var/ls': Permission denied\r\n/bin/cp: cannot create regular file '/var/mv': Permission denied\r\n/bin/cp: cannot create regular file '/var/nl': Permission denied\r\n/bin/cp: cannot create regular file '/var/od': Permission denied\r\n/bin/cp: cannot create regular file '/var/pr': Permission denied\r\n/bin/cp: cannot create regular file '/var/ps': Permission denied\r\n/bin/cp: cannot create regular file '/var/rm': Permission denied\r\n/bin/cp: cannot create regular file '/var/sg': Permission denied\r\n/bin/cp: cannot create regular file '/var/sh': Permission denied\r\n/bin/cp: cannot create regular file '/var/su': Permission denied\r\n/bin/cp: cannot create regular file '/var/tr': Permission denied\r\n/bin/cp: cannot create regular file '/var/wc': Permission denied\r\n/bin/cp: -r not specified; omitting directory '/dev/fd'\r\n/bin/cp: -r not specified; omitting directory '/sys/fs'\r\n/bin/cp: -r not specified; omitting directory '/proc/acpi'\r\n/bin/cp: cannot create regular file '/var/keys': Permission denied\r\n/bin/cp: cannot open '/proc/kmsg' for reading: Permission denied\r\n/bin/cp: cannot create regular file '/var/misc': Permission denied\r\n/bin/cp: cannot open '/proc/mtrr' for reading: Operation not permitted\r\n/bin/cp: -r not specified; omitting directory '/proc/self'\r\n/bin/cp: cannot create regular file '/var/stat': Permission denied\r\n/bin/cp: cannot create regular file '/var/cpgr': Permission denied\r\n/bin/cp: cannot create regular file '/var/cppw': Permission denied\r\n/bin/cp: cannot create regular file '/var/fsck': Permission denied\r\n/bin/cp: cannot create regular file '/var/mkfs': Pe"}, {"payload": "/???/????", "response": "rmission denied\r\n/bin/cp: cannot create regular file '/var/pwck': Permission denied\r\n/bin/cp: cannot create regular file '/var/vigr': Permission denied\r\n/bin/cp: cannot create regular file '/var/vipw': Permission denied\r\n/bin/cp: -r not specified; omitting directory '/bin'\r\n/bin/cp: -r not specified; omitting directory '/dev'\r\n/bin/cp: -r not specified; omitting directory '/etc'\r\n/bin/cp: -r not specified; omitting directory '/lib'\r\n/bin/cp: -r not specified; omitting directory '/mnt'\r\n/bin/cp: -r not specified; omitting directory '/opt'\r\n/bin/cp: -r not specified; omitting directory '/run'\r\n/bin/cp: -r not specified; omitting directory '/srv'\r\n/bin/cp: -r not specified; omitting directory '/sys'\r\n/bin/cp: -r not specified; omitting directory '/tmp'\r\n/bin/cp: -r not specified; omitting directory '/usr'\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m/bin/[: missing ']'\r\n\u001b[32mB<PERSON>n@Shell\u001b[33m$ \u001b[0m/bin/[: missing ']'\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m/bin/[: missing ']'\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m/bin/arch: extra operand '/bin/bash'\r\nTry '/bin/arch --help' for more information.\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "/???/??? /???/????", "response": "\u001b[1;31mE: \u001b[0mInvalid operation /bin/awk\u001b[0m\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "/???/??? /????/????", "response": "\u001b[1;31mE: \u001b[0mInvalid operation /bin/awk\u001b[0m\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "/???/??? /?????", "response": "\u001b[1;31mE: \u001b[0mInvalid operation /bin/awk\u001b[0m\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$0", "response": "TERM environment variable not set.\r\n\u001b[1;34m\r\n⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⢀⣀⣀⠀⠀⣀⣄⣀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀\r\n⠀⠀⠀⠀⠀⠀⠀⠀⠀⢠⡴⠦⣤⣴⡟⠉⠉⡗⠚⡇⠀⠈⡷⢤⠖⠒⠲⡄⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀\r\n⠀⠀⠀⠀⠀⠀⠀⢀⣀⡇⠀⠀⢸⠀⡇⠀⠀⠇⠀⡇⠀⠀⡇⢸⠀⠀⢠⠋⡝⠉⠓⢦⠀⠀⠀⠀⠀⠀⠀⠀\r\n⠀⠀⠀⠀⠀⢠⠊⠁⢣⢰⠀⠀⠈⡆⢳⠀⠀⠀⠀⡇⠀⢸⠀⡇⠀⠀⡜⢰⠃⠀⠀⡜⢦⢤⣀⠀⠀⠀⠀⠀\r\n⠀⠀⠀⡠⢴⠻⡀⠀⠈⡎⡇⠀⠀⢃⢸⠀⠀⢸⠀⡇⠀⢸⢀⡇⠀⢠⠃⡎⠀⠀⡼⢡⠃⠀⠙⡆⠀⠀⠀⠀\r\n⠀⠀⢸⠁⠀⢣⢱⡀⠀⢸⣸⡀⠀⢸⠈⡄⠀⢸⠀⡇⠀⠘⢸⠀⠀⡸⢰⠁⠀⢰⢡⠏⠀⠀⡜⡿⢤⡀⠀⠀\r\n⠀⣠⠞⣷⡄⠀⢣⢣⠀⠀⢧⢇⠀⠈⡆⡇⠀⢸⠀⡇⠀⡇⡼⠀⢀⠇⡏⠀⢠⢇⠎⠀⢀⡜⡜⠁⠀⢳⠀⠀\r\n⢸⡁⠀⠈⢻⣦⠀⢫⢧⠀⠘⡾⡄⠀⣇⢡⠀⢸⠀⡇⠀⡇⡇⠀⣸⢸⠀⢀⠏⡜⠀⢀⢎⠞⠀⢀⡴⡻⡄⠀\r\n⣰⠻⣆⠀⠀⠻⣷⡀⢻⣇⠀⢹⢧⠀⢸⢸⠀⢸⠀⡇⢠⢿⠃⠀⡇⡇⠀⡜⡾⠀⢠⢾⠎⠀⡰⢫⠞⠀⡟⠀\r\n⢹⣅⠙⢷⣄⠀⠙⣷⡀⢻⡄⠈⣿⡄⠘⡞⡆⢸⠀⡇⢸⢸⠀⢸⣸⠀⡸⡽⠁⢠⣯⠋⣠⣾⡖⢁⡠⠚⣿⡄\r\n⠘⣏⠳⣄⠙⣦⡀⠘⢧⠀⢻⡄⠸⣧⠀⠇⡇⢸⠀⡇⢸⡌⠀⣇⠇⢰⣱⠁⣠⡻⢃⡴⡷⢋⡴⢋⡤⣺⠇⠀\r\n⠀⢹⡕⢄⠑⢌⠛⢦⠈⢣⡀⢻⡀⢳⠀⢸⢳⢸⠀⡇⣎⡇⢸⠽⢠⠇⠁⣰⡿⢡⣾⢞⣴⣯⡔⣫⢴⠟⠀⠀\r\n⠀⠀⠙⢦⣝⢦⡑⢄⠳⣌⠳⡈⢧⠈⡆⠸⣼⠸⡀⡇⡏⠁⡏⢀⠎⠀⡰⠉⡱⠋⡡⣫⣞⣽⣋⣴⠟⠀⠀⠀\r\n⠀⠀⠀⠀⠉⠻⢿⣮⡳⣌⢦⡱⡌⣆⢸⠀⡟⡆⡇⡇⡇⢸⠁⡜⠀⡼⠁⡜⠁⣪⠞⣿⡵⠟⠉⠀⠀⠀⠀⠀\r\n⠀⠀⠀⠀⠀⠀⠀⠙⡾⣾⣷⣵⡜⡜⡄⠇⢣⡇⡇⢳⠁⡎⠀⠀⡼⢁⠞⠠⢾⡵⠟⠉⠀⠀⠀⠀⠀⠀⠀⠀\r\n⠀⠀⠀⠀⠀⠀⠀⠀⣷⠈⠛⢿⣾⣾⣾⠘⠈⠁⠇⠀⠀⠁⠀⠾⠁⢀⣤⠞⢫⡗⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀\r\n⠀⠀⠀⠀⠀⠀⠀⠀⠘⢧⣄⠀⠙⠿⣟⠀⠀⠀⠀⠀⠀⠀⠀⢀⡴⠛⣡⡴⠋⠁⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀\r\n⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠈⠙⠒⠦⠬⠷⢄⡀⠀⠀⠀⣠⠴⠟⠚⠉⠁⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀\r\n⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠉⠛⠒⠋⠁⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀\r\n\r\n\u001b[33m\r\n[*] Allowed characters: ^[0-9${}/?\"[:space:]:&>_=()]+$\r\n\u001b[0m\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$((10/2))", "response": "/home/<USER>/broken_shell.sh: line 41: 5: command not found\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$((11/2))", "response": "/home/<USER>/broken_shell.sh: line 41: 5: command not found\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$((20/5))", "response": "/home/<USER>/broken_shell.sh: line 41: 4: command not found\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "/???/?? >&2", "response": "/bin/cp: cannot create regular file '/sys/fs/dd': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/df': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/du': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/id': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/ln': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/ls': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/mv': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/nl': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/od': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/pr': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/ps': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/rm': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/sg': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/sh': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/su': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/tr': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/wc': Read-only file system\r\n/bin/cp: -r not specified; omitting directory '/dev/fd'\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "/???/?? 2>&1", "response": "/bin/cp: cannot create regular file '/sys/fs/dd': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/df': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/du': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/id': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/ln': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/ls': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/mv': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/nl': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/od': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/pr': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/ps': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/rm': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/sg': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/sh': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/su': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/tr': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/wc': Read-only file system\r\n/bin/cp: -r not specified; omitting directory '/dev/fd'\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "/???/???/?", "response": "/home/<USER>/broken_shell.sh: line 41: /dev/pts/1: Permission denied\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "/???/???/??", "response": "/usr/bin/cp: target '/usr/bin/wc' is not a directory\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "/???/???/???", "response": "/home/<USER>/broken_shell.sh: line 41: /sys/bus/cpu: Is a directory\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "/???/??", "response": "/bin/cp: cannot create regular file '/sys/fs/dd': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/df': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/du': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/id': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/ln': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/ls': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/mv': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/nl': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/od': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/pr': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/ps': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/rm': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/sg': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/sh': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/su': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/tr': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/wc': Read-only file system\r\n/bin/cp: -r not specified; omitting directory '/dev/fd'\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "/???/????", "response": "/bin/arch: extra operand '/bin/bash'\r\nTry '/bin/arch --help' for more information.\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$?", "response": "/home/<USER>/broken_shell.sh: line 41: 0: command not found\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$$", "response": "/home/<USER>/broken_shell.sh: line 41: 107512: command not found\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "/???/???", "response": "\u001b[1;31mE: \u001b[0mInvalid operation /bin/awk\u001b[0m\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "/???/??? /???/????", "response": "\u001b[1;31mE: \u001b[0mInvalid operation /bin/awk\u001b[0m\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "/???/??? /????/????", "response": "\u001b[1;31mE: \u001b[0mInvalid operation /bin/awk\u001b[0m\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "/???/??? /?????", "response": "\u001b[1;31mE: \u001b[0mInvalid operation /bin/awk\u001b[0m\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "/???/??", "response": "/bin/cp: cannot create regular file '/sys/fs/dd': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/df': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/du': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/id': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/ln': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/ls': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/mv': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/nl': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/od': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/pr': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/ps': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/rm': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/sg': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/sh': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/su': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/tr': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/wc': Read-only file system\r\n/bin/cp: -r not specified; omitting directory '/dev/fd'\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "/???/?? /", "response": "/bin/cp: cannot create regular file '/dd': Permission denied\r\n/bin/cp: cannot create regular file '/df': Permission denied\r\n/bin/cp: cannot create regular file '/du': Permission denied\r\n/bin/cp: cannot create regular file '/id': Permission denied\r\n/bin/cp: cannot create regular file '/ln': Permission denied\r\n/bin/cp: cannot create regular file '/ls': Permission denied\r\n/bin/cp: cannot create regular file '/mv': Permission denied\r\n/bin/cp: cannot create regular file '/nl': Permission denied\r\n/bin/cp: cannot create regular file '/od': Permission denied\r\n/bin/cp: cannot create regular file '/pr': Permission denied\r\n/bin/cp: cannot create regular file '/ps': Permission denied\r\n/bin/cp: cannot create regular file '/rm': Permission denied\r\n/bin/cp: cannot create regular file '/sg': Permission denied\r\n/bin/cp: cannot create regular file '/sh': Permission denied\r\n/bin/cp: cannot create regular file '/su': Permission denied\r\n/bin/cp: cannot create regular file '/tr': Permission denied\r\n/bin/cp: cannot create regular file '/wc': Permission denied\r\n/bin/cp: -r not specified; omitting directory '/dev/fd'\r\n/bin/cp: -r not specified; omitting directory '/sys/fs'\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "/???/?? /???", "response": "/bin/cp: cannot create regular file '/var/dd': Permission denied\r\n/bin/cp: cannot create regular file '/var/df': Permission denied\r\n/bin/cp: cannot create regular file '/var/du': Permission denied\r\n/bin/cp: cannot create regular file '/var/id': Permission denied\r\n/bin/cp: cannot create regular file '/var/ln': Permission denied\r\n/bin/cp: cannot create regular file '/var/ls': Permission denied\r\n/bin/cp: cannot create regular file '/var/mv': Permission denied\r\n/bin/cp: cannot create regular file '/var/nl': Permission denied\r\n/bin/cp: cannot create regular file '/var/od': Permission denied\r\n/bin/cp: cannot create regular file '/var/pr': Permission denied\r\n/bin/cp: cannot create regular file '/var/ps': Permission denied\r\n/bin/cp: cannot create regular file '/var/rm': Permission denied\r\n/bin/cp: cannot create regular file '/var/sg': Permission denied\r\n/bin/cp: cannot create regular file '/var/sh': Permission denied\r\n/bin/cp: cannot create regular file '/var/su': Permission denied\r\n/bin/cp: cannot create regular file '/var/tr': Permission denied\r\n/bin/cp: cannot create regular file '/var/wc': Permission denied\r\n/bin/cp: -r not specified; omitting directory '/dev/fd'\r\n/bin/cp: -r not specified; omitting directory '/sys/fs'\r\n/bin/cp: -r not specified; omitting directory '/bin'\r\n/bin/cp: -r not specified; omitting directory '/dev'\r\n/bin/cp: -r not specified; omitting directory '/etc'\r\n/bin/cp: -r not specified; omitting directory '/lib'\r\n/bin/cp: -r not specified; omitting directory '/mnt'\r\n/bin/cp: -r not specified; omitting directory '/opt'\r\n/bin/cp: -r not specified; omitting directory '/run'\r\n/bin/cp: -r not specified; omitting directory '/srv'\r\n/bin/cp: -r not specified; omitting directory '/sys'\r\n/bin/cp: -r not specified; omitting directory '/tmp'\r\n/bin/cp: -r not specified; omitting directory '/usr'\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "/???/?? /????", "response": "/bin/cp: cannot create regular file '/sbin/dd': Permission denied\r\n/bin/cp: cannot create regular file '/sbin/df': Permission denied\r\n/bin/cp: cannot create regular file '/sbin/du': Permission denied\r\n/bin/cp: cannot create regular file '/sbin/id': Permission denied\r\n/bin/cp: cannot create regular file '/sbin/ln': Permission denied\r\n/bin/cp: cannot create regular file '/sbin/ls': Permission denied\r\n/bin/cp: cannot create regular file '/sbin/mv': Permission denied\r\n/bin/cp: cannot create regular file '/sbin/nl': Permission denied\r\n/bin/cp: cannot create regular file '/sbin/od': Permission denied\r\n/bin/cp: cannot create regular file '/sbin/pr': Permission denied\r\n/bin/cp: cannot create regular file '/sbin/ps': Permission denied\r\n/bin/cp: cannot create regular file '/sbin/rm': Permission denied\r\n/bin/cp: cannot create regular file '/sbin/sg': Permission denied\r\n/bin/cp: cannot create regular file '/sbin/sh': Permission denied\r\n/bin/cp: cannot create regular file '/sbin/su': Permission denied\r\n/bin/cp: cannot create regular file '/sbin/tr': Permission denied\r\n/bin/cp: cannot create regular file '/sbin/wc': Permission denied\r\n/bin/cp: -r not specified; omitting directory '/dev/fd'\r\n/bin/cp: -r not specified; omitting directory '/sys/fs'\r\n/bin/cp: -r not specified; omitting directory '/boot'\r\n/bin/cp: -r not specified; omitting directory '/home'\r\n/bin/cp: -r not specified; omitting directory '/proc'\r\n/bin/cp: -r not specified; omitting directory '/root'\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "/???/?? /????/????", "response": "/bin/cp: target '/sbin/vipw' is not a directory\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "/???/????", "response": "/bin/arch: extra operand '/bin/bash'\r\nTry '/bin/arch --help' for more information.\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "/???/???? / -???? ????", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}]