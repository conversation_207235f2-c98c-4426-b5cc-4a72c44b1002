[{"payload": "$?", "response": "⢳⠀⢸⢳⢸⠀⡇⣎⡇⢸⠽⢠⠇⠁⣰⡿⢡⣾⢞⣴⣯⡔⣫⢴⠟⠀⠀\r\n⠀⠀⠙⢦⣝⢦⡑⢄⠳⣌⠳⡈⢧⠈⡆⠸⣼⠸⡀⡇⡏⠁⡏⢀⠎⠀⡰⠉⡱⠋⡡⣫⣞⣽⣋⣴⠟⠀⠀⠀\r\n⠀⠀⠀⠀⠉⠻⢿⣮⡳⣌⢦⡱⡌⣆⢸⠀⡟⡆⡇⡇⡇⢸⠁⡜⠀⡼⠁⡜⠁⣪⠞⣿⡵⠟⠉⠀⠀⠀⠀⠀\r\n⠀⠀⠀⠀⠀⠀⠀⠙⡾⣾⣷⣵⡜⡜⡄⠇⢣⡇⡇⢳⠁⡎⠀⠀⡼⢁⠞⠠⢾⡵⠟⠉⠀⠀⠀⠀⠀⠀⠀⠀\r\n⠀⠀⠀⠀⠀⠀⠀⠀⣷⠈⠛⢿⣾⣾⣾⠘⠈⠁⠇⠀⠀⠁⠀⠾⠁⢀⣤⠞⢫⡗⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀\r\n⠀⠀⠀⠀⠀⠀⠀⠀⠘⢧⣄⠀⠙⠿⣟⠀⠀⠀⠀⠀⠀⠀⠀⢀⡴⠛⣡⡴⠋⠁⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀\r\n⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠈⠙⠒⠦⠬⠷⢄⡀⠀⠀⠀⣠⠴⠟⠚⠉⠁⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀\r\n⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠉⠛⠒⠋⠁⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀⠀\r\n\r\n\u001b[33m\r\n[*] Allowed characters: ^[0-9${}/?\"[:space:]:&>_=()]+$\r\n\u001b[0m\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m/home/<USER>/broken_shell.sh: line 41: 0: command not found\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$$", "response": "/home/<USER>/broken_shell.sh: line 41: 315: command not found\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$PATH", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$PWD", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$HOME", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$SHELL", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$USER", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "/???/???", "response": "\u001b[1;31mE: \u001b[0mInvalid operation /bin/awk\u001b[0m\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "/???/??", "response": "/bin/cp: cannot create regular file '/sys/fs/dd': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/df': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/du': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/id': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/ln': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/ls': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/mv': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/nl': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/od': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/pr': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/ps': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/rm': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/sg': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/sh': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/su': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/tr': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/wc': Read-only file system\r\n/bin/cp: -r not specified; omitting directory '/dev/fd'\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "/?/?", "response": "/home/<USER>/broken_shell.sh: line 41: /?/?: No such file or directory\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "/??/?", "response": "/home/<USER>/broken_shell.sh: line 41: /??/?: No such file or directory\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "/???/?", "response": "/bin/[: missing ']'\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/??)", "response": "/bin/cp: cannot create regular file '/sys/fs/dd': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/df': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/du': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/id': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/ln': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/ls': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/mv': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/nl': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/od': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/pr': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/ps': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/rm': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/sg': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/sh': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/su': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/tr': Read-only file system\r\n/bin/cp: cannot create regular file '/sys/fs/wc': Read-only file system\r\n/bin/cp: -r not specified; omitting directory '/dev/fd'\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/???)", "response": "\r\nWARNING: apt does not have a stable CLI interface. Use with caution in scripts.\r\n\r\nE: Invalid operation /bin/awk\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/??/?)", "response": "/home/<USER>/broken_shell.sh: line 41: /dev/fd/0: Permission denied\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": ">&2", "response": "\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "2>&1", "response": "\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "5>&1", "response": "\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "5>&2", "response": "\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$((1+1))", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$((PATH))", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$((PWD))", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/??? ????)", "response": "\r\nWARNING: apt does not have a stable CLI interface. Use with caution in scripts.\r\n\r\nE: Invalid operation /bin/awk\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/??? ?????)", "response": "\r\nWARNING: apt does not have a stable CLI interface. Use with caution in scripts.\r\n\r\nE: Invalid operation /bin/awk\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/??? ??????)", "response": "\r\nWARNING: apt does not have a stable CLI interface. Use with caution in scripts.\r\n\r\nE: Invalid operation /bin/awk\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/??? ???????)", "response": "\r\nWARNING: apt does not have a stable CLI interface. Use with caution in scripts.\r\n\r\nE: Invalid operation /bin/awk\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/??? ????????)", "response": "\r\nWARNING: apt does not have a stable CLI interface. Use with caution in scripts.\r\n\r\nE: Invalid operation /bin/awk\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/??? ?????????)", "response": "\r\nWARNING: apt does not have a stable CLI interface. Use with caution in scripts.\r\n\r\nE: Invalid operation /bin/awk\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/??? ??????????)", "response": "\r\nWARNING: apt does not have a stable CLI interface. Use with caution in scripts.\r\n\r\nE: Invalid operation /bin/awk\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/??? /????/????)", "response": "\r\nWARNING: apt does not have a stable CLI interface. Use with caution in scripts.\r\n\r\nE: Invalid operation /bin/awk\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/??? /????)", "response": "\r\nWARNING: apt does not have a stable CLI interface. Use with caution in scripts.\r\n\r\nE: Invalid operation /bin/awk\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/??? /????/????/????)", "response": "\r\nWARNING: apt does not have a stable CLI interface. Use with caution in scripts.\r\n\r\nE: Invalid operation /bin/awk\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/??? /???/?????????) >&2", "response": "\r\nWARNING: apt does not have a stable CLI interface. Use with caution in scripts.\r\n\r\nE: Invalid operation /bin/awk\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/??? /???/?????????) > /???/????", "response": "\r\nWARNING: apt does not have a stable CLI interface. Use with caution in scripts.\r\n\r\nE: Invalid operation /bin/awk\r\n/home/<USER>/broken_shell.sh: line 41: /???/????: ambiguous redirect\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}, {"payload": "$(/???/??? /???/?????????) > /dev/fd/2", "response": "\r\n\u001b[31m[-] Error: Command contains disallowed characters.\u001b[0m\r\n\r\n\u001b[32mBroken@Shell\u001b[33m$ \u001b[0m"}]